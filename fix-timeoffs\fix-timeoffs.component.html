<div
  class="dynamic-shadow p-3 m-4 bg-white"
  fxLayout="column"
  fxLayoutAlign="space-around"
>
  <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="10px">
    <app-select-doctor
      [doctors]="allowedDoctors"
      [selectedDoctors]="doctors"
      (doctorIDsChangeEvent)="selectDoctorChange($event)"
    ></app-select-doctor>

    <mat-button-toggle-group name="fontStyle" aria-label="Font Style">
      <mat-button-toggle
        fxFlex
        *ngFor="let day of [1, 2, 3, 4, 5, 6, 0]"
        (click)="onDayChange(day)"
        [checked]="day === selectedDay"
        [value]="day"
      >
        {{ getDayOfWeek(day) | translate }}
      </mat-button-toggle>
    </mat-button-toggle-group>
    <app-circle-button
      name="add"
      (click)="createTimeoffClick()"
    ></app-circle-button>
  </div>
  <div *ngFor="let timeoff of timeoffs">
    <app-timeoff
      [dir]="dir"
      [allowEdit]="true"
      [timeoff]="timeoff"
      (timeoffUpdatedEvent)="manageUpdateTimeoff($event)"
      [showCountDown]="false"
      [fromDaily]="false"
    ></app-timeoff>
  </div>
  <div *ngIf="!timeoffs || timeoffs.length === 0">
    <app-no-results [isSmall]="true" [showImage]="false">
      {{ 'config.fixBreaks.noBreaksFound' | translate }}
    </app-no-results>
  </div>
</div>
<ng-template #loader>
  <app-session *ngIf="isLoading"></app-session>
</ng-template>
