import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import * as moment from 'moment';
import { NavigationService } from 'src/app/core/services/navigation.service';
import { SocketService } from 'src/app/core/services/socket.service';
import { StorageService } from 'src/app/core/services/storage.service';
import { getDayOfWeek } from '../../constants/date.consts';
import { Profile } from '../../models/profile.model';
import { Timeoff } from '../../models/timeoff.model';
import { AppointmentService } from '../../services/appointment.service';
import { NotificationService } from '../../services/notification.service';
import { TimeoffDialogComponent } from '../timeoff-dialog/timeoff-dialog.component';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-fix-timeoffs',
  templateUrl: './fix-timeoffs.component.html',
  styleUrls: ['./fix-timeoffs.component.scss'],
})
export class FixTimeoffsComponent implements OnInit {
  @Input() dir: Direction = 'ltr';
  @Output() changeStatus: EventEmitter<boolean> = new EventEmitter<boolean>();

  public isLoading: boolean = false;
  public timeoffs: Timeoff[] = [];
  public selectedDay: number = 1;
  public getDayOfWeek = getDayOfWeek;
  public dateForTimeOffs: Date = moment()
    .utcOffset(0)
    .set({ hour: 0, minute: 0, second: 0 })
    .toDate();
  public allowedDoctors: Profile[] = [];
  public doctors: string[] = [];

  constructor(
    private appointmentService: AppointmentService,
    private storageService: StorageService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getDoctors();
    this.getTimeoffs();
  }

  getDoctors() {
    this.allowedDoctors = this.storageService.getDoctors();
    this.doctors = this.storageService
      .getDoctorsByDefault()
      .map((x) => x._id || '')
      .filter((x) => x !== '');
  }

  sortTimeoffs() {
    this.timeoffs = this.timeoffs.sort((a: Timeoff, b: Timeoff) => {
      if (a.startTime && b.startTime) {
        return (
          new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
        );
      } else {
        return 0;
      }
    });
  }

  getTimeoffs() {
    this.appointmentService
      .getTimeOffs(undefined, this.doctors, this.selectedDay + '')
      .subscribe((res: any) => {
        if (res.timeoffs) {
          this.timeoffs = res.timeoffs;
        }
      });
  }

  createTimeoffClick() {
    const dialogRef = this.dialog.open(TimeoffDialogComponent, {
      width: '450px',
      data: {
        type: 'CREATE',
        timeoff: {
          date: new Date('2000-01-02'),
          day: this.selectedDay,
        },
        fromDaily: false,
        selectedDay: this.selectedDay,
      },
    });

    dialogRef.afterClosed().subscribe((timeoff) => {
      if (timeoff && timeoff._id) {
        this.timeoffs.push(timeoff);
        this.sortTimeoffs();
        this.changeStatus.emit(true); // Emit save status
      }
    });
  }

  onDayChange(day: number) {
    this.selectedDay = day;
    this.getTimeoffs();
  }

  manageUpdateTimeoff(event: any) {
    const timeoff = event?.timeoff;
    const deleted = event?.deleted;
    if (deleted && !timeoff.isDaily) {
      this.timeoffs = this.timeoffs.filter(
        (x: Timeoff) => x._id !== timeoff._id
      );
    } else {
      const index = this.timeoffs.findIndex(
        (x: Timeoff) => x._id === timeoff._id
      );
      this.timeoffs[index] = timeoff;
      this.sortTimeoffs();
    }
    this.changeStatus.emit(true); // Emit save status
  }

  save() {
    // Timeoffs are saved automatically when created/updated
    // This method is called by the parent config component
    this.changeStatus.emit(true);
  }

  selectDoctorChange(doctors: any) {
    this.doctors = doctors;
    this.getTimeoffs();
  }
}
