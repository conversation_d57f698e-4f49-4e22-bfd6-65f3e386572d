<div class="config-container">
  <mat-tab-group mat-align-tabs="center" (selectedTabChange)="onTabChange($event)">
    <mat-tab [label]="'config.office' | translate">
      <app-hospital
        #hospitalComponent
        (changeStatus)="handleSaveChange($event)"
        [hospital]="hospital">
      </app-hospital>
    </mat-tab>
    <mat-tab [label]="'config.breaks' | translate">
      <app-fix-timeoffs
        #timeoffsComponent
        [dir]="dir"
        (changeStatus)="handleSaveChange($event)">
      </app-fix-timeoffs>
    </mat-tab>
    <mat-tab [label]="'config.team' | translate">
      <app-staff
        #staffComponent
        [dir]="dir"
        (changeStatus)="handleSaveChange($event)">
      </app-staff>
    </mat-tab>
    <mat-tab [label]="'config.others' | translate">
      <app-other-configurations
        #otherConfigsComponent
        [dir]="dir"
        (changeStatus)="handleSaveChange($event)">
      </app-other-configurations>
    </mat-tab>
  </mat-tab-group>

  <!-- Global Save Button -->
  <div class="save-button-container" *ngIf="showSaveButton">
    <app-custom-button
      color="primary"
      [loading]="isSaving"
      [disabled]="!hasUnsavedChanges"
      (click)="saveCurrentTab()">
      {{ 'config.generalInfo.save' | translate }}
    </app-custom-button>
  </div>
</div>
