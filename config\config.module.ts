import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { ConfigRoutingModule } from './config-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { ConfigComponent } from './config.component';
import { TranslateModule } from '@ngx-translate/core';
import { OtherConfigurationsComponent } from './other-configurations/other-configurations.component';
import {MatCheckboxModule} from "@angular/material/checkbox";

@NgModule({
  declarations: [ConfigComponent, OtherConfigurationsComponent],
    imports: [
        CommonModule,
        ConfigRoutingModule,
        SharedModule,
        MatTabsModule,
        TranslateModule,
        MatCheckboxModule,
    ],
})
export class ConfigModule {}
